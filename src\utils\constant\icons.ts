import { iconGenerator } from '@/utils/tools/icon-generator'

// 初始化图标生成器，加载所有SVG图标
await iconGenerator.loadIconsFromGlob('@/assets/images/icons/*.svg', {
  eager: true,
  nameTransform: (fileName: string) => {
    // 将文件名转换为PascalCase
    return fileName.charAt(0).toUpperCase() + fileName.slice(1)
  },
})

// 导出所有图标组件
export const Icons = iconGenerator.getRegistry()
console.log('🚀 ~ Icons:', Icons)

// 导出单个图标（保持向后兼容）
export const SearchIcon = iconGenerator.getIcon('Search')

// 导出图标名称列表
export const IconNames = iconGenerator.getIconNames()

// 工具函数：根据名称获取图标组件
export const getIcon = (name: string) => {
  return iconGenerator.getIcon(name)
}

// 工具函数：获取所有可用的图标名称
export const getAvailableIcons = () => {
  return iconGenerator.getIconNames()
}

// 工具函数：检查图标是否存在
export const hasIcon = (name: string) => {
  return iconGenerator.hasIcon(name)
}

// 工具函数：获取图标配置信息
export const getIconConfigs = () => {
  return iconGenerator.getIconConfigs()
}
