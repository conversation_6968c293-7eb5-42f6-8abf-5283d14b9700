<template>
  <div class="icon-demo p-4">
    <h3 class="text-lg font-bold mb-4">图标使用示例</h3>
    
    <!-- 方式1: 直接使用具名导出的图标 -->
    <div class="mb-4">
      <h4 class="text-md font-semibold mb-2">方式1: 直接使用具名导出</h4>
      <div class="flex items-center gap-2">
        <component :is="SearchIcon()" />
        <span>搜索图标</span>
      </div>
    </div>

    <!-- 方式2: 通过名称动态获取图标 -->
    <div class="mb-4">
      <h4 class="text-md font-semibold mb-2">方式2: 动态获取图标</h4>
      <div class="flex items-center gap-2">
        <component :is="getIcon('Search')?.()" v-if="getIcon('Search')" />
        <span>动态搜索图标</span>
      </div>
    </div>

    <!-- 方式3: 显示所有可用图标 -->
    <div class="mb-4">
      <h4 class="text-md font-semibold mb-2">方式3: 所有可用图标</h4>
      <div class="grid grid-cols-4 gap-4">
        <div 
          v-for="iconName in getAvailableIcons()" 
          :key="iconName"
          class="flex flex-col items-center p-2 border rounded hover:bg-gray-50"
        >
          <component :is="getIcon(iconName)?.()" v-if="getIcon(iconName)" />
          <span class="text-xs mt-1">{{ iconName }}</span>
        </div>
      </div>
    </div>

    <!-- 方式4: 在按钮中使用图标 -->
    <div class="mb-4">
      <h4 class="text-md font-semibold mb-2">方式4: 在按钮中使用</h4>
      <n-button type="primary" :render-icon="SearchIcon()">
        搜索按钮
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui'
import { SearchIcon, getIcon, getAvailableIcons } from '@/utils/constant/icons'
</script>

<style scoped>
.icon-demo {
  max-width: 800px;
  margin: 0 auto;
}
</style>
