import { renderIcon } from '@/utils/tools/render'
import { defineAsyncComponent, type Component } from 'vue'
import type { IconRegistry, IconConfig } from '@/types/icons'

/**
 * 图标生成器类
 * 用于动态解析SVG文件并生成Vue图标组件
 */
export class IconGenerator {
  private iconRegistry: IconRegistry = {}
  private iconConfigs: IconConfig[] = []

  /**
   * 从指定路径批量导入SVG图标
   * @param globPattern - glob模式，如 '@/assets/images/icons/*.svg'
   * @param options - 配置选项
   */
  async loadIconsFromGlob(
    globPattern: string,
    options: {
      eager?: boolean
      nameTransform?: (fileName: string) => string
    } = {},
  ) {
    const { eager = true, nameTransform = this.defaultNameTransform } = options

    // 动态导入SVG文件
    const modules = import.meta.glob(globPattern, { eager })

    // 处理每个SVG文件
    Object.entries(modules).forEach(([path, _module]) => {
      const fileName = this.extractFileName(path)
      const componentName = nameTransform(fileName)

      // 创建异步组件
      const SvgComponent = defineAsyncComponent(() => import(path))

      // 使用renderIcon包装
      const iconComponent = () => renderIcon(SvgComponent as Component)

      // 注册图标
      this.registerIcon(componentName, iconComponent, path)
    })
  }

  /**
   * 手动注册单个图标
   * @param name - 图标名称
   * @param component - 图标组件
   * @param path - 图标路径（可选）
   */
  registerIcon(name: string, component: () => ReturnType<typeof renderIcon>, path?: string) {
    this.iconRegistry[name] = component
    this.iconConfigs.push({
      name,
      component,
      path: path || '',
    })
  }

  /**
   * 获取图标组件
   * @param name - 图标名称
   * @returns 图标组件或null
   */
  getIcon(name: string) {
    return this.iconRegistry[name] || null
  }

  /**
   * 获取所有图标名称
   * @returns 图标名称数组
   */
  getIconNames(): string[] {
    return Object.keys(this.iconRegistry)
  }

  /**
   * 获取所有图标配置
   * @returns 图标配置数组
   */
  getIconConfigs(): IconConfig[] {
    return [...this.iconConfigs]
  }

  /**
   * 获取图标注册表
   * @returns 图标注册表
   */
  getRegistry(): IconRegistry {
    return { ...this.iconRegistry }
  }

  /**
   * 检查图标是否存在
   * @param name - 图标名称
   * @returns 是否存在
   */
  hasIcon(name: string): boolean {
    return name in this.iconRegistry
  }

  /**
   * 清空所有图标
   */
  clear() {
    this.iconRegistry = {}
    this.iconConfigs = []
  }

  /**
   * 从路径中提取文件名
   * @param path - 文件路径
   * @returns 文件名（不含扩展名）
   */
  private extractFileName(path: string): string {
    return path.split('/').pop()?.replace('.svg', '') || ''
  }

  /**
   * 默认的名称转换函数（转换为PascalCase）
   * @param fileName - 文件名
   * @returns 转换后的组件名
   */
  private defaultNameTransform(fileName: string): string {
    return fileName
      .split(/[-_]/)
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
      .join('')
  }
}

// 创建全局图标生成器实例
export const iconGenerator = new IconGenerator()

// 便捷函数
export const loadIcons = (
  globPattern: string,
  options?: Parameters<IconGenerator['loadIconsFromGlob']>[1],
) => {
  return iconGenerator.loadIconsFromGlob(globPattern, options)
}

export const getIcon = (name: string) => iconGenerator.getIcon(name)
export const getIconNames = () => iconGenerator.getIconNames()
export const hasIcon = (name: string) => iconGenerator.hasIcon(name)
