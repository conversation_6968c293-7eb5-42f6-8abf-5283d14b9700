# 图标系统使用指南

本项目提供了一套完整的SVG图标管理系统，支持自动解析SVG文件并生成Vue组件。

## 功能特性

- 🚀 自动解析 `src/assets/images/icons/` 目录下的所有SVG文件
- 📦 使用 `renderIcon` 函数包装，与 Naive UI 完美集成
- 🎯 支持动态导入和按需加载
- 🔧 提供多种使用方式和工具函数
- 📝 完整的TypeScript类型支持

## 目录结构

```
src/
├── assets/images/icons/     # SVG图标文件目录
│   └── Search.svg          # 示例图标
├── utils/
│   ├── constant/
│   │   └── icons.ts        # 图标导出文件
│   └── tools/
│       ├── render/index.ts # renderIcon函数
│       └── icon-generator.ts # 图标生成器
├── types/
│   └── icons.ts           # 图标类型定义
└── components/common/
    └── IconDemo.vue       # 使用示例组件
```

## 使用方法

### 1. 添加新图标

将SVG文件放入 `src/assets/images/icons/` 目录，系统会自动识别并生成对应的Vue组件。

文件命名规则：
- `search.svg` → `SearchIcon`
- `user-profile.svg` → `UserProfileIcon`
- `arrow_down.svg` → `ArrowDownIcon`

### 2. 在组件中使用图标

#### 方式一：直接导入使用

```vue
<template>
  <div>
    <!-- 在模板中使用 -->
    <component :is="SearchIcon()" />
    
    <!-- 在按钮中使用 -->
    <n-button type="primary" :render-icon="SearchIcon()">
      搜索
    </n-button>
  </div>
</template>

<script setup lang="ts">
import { SearchIcon } from '@/utils/constant/icons'
import { NButton } from 'naive-ui'
</script>
```

#### 方式二：动态获取图标

```vue
<template>
  <div>
    <component :is="getIcon('Search')?.()" v-if="getIcon('Search')" />
  </div>
</template>

<script setup lang="ts">
import { getIcon } from '@/utils/constant/icons'
</script>
```

#### 方式三：遍历所有图标

```vue
<template>
  <div class="icon-grid">
    <div 
      v-for="iconName in getAvailableIcons()" 
      :key="iconName"
      class="icon-item"
    >
      <component :is="getIcon(iconName)?.()" />
      <span>{{ iconName }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getIcon, getAvailableIcons } from '@/utils/constant/icons'
</script>
```

### 3. 工具函数

```typescript
import { 
  getIcon,           // 获取指定图标
  getAvailableIcons, // 获取所有图标名称
  hasIcon,           // 检查图标是否存在
  getIconConfigs     // 获取图标配置信息
} from '@/utils/constant/icons'

// 检查图标是否存在
if (hasIcon('Search')) {
  const searchIcon = getIcon('Search')
}

// 获取所有可用图标
const allIcons = getAvailableIcons()
console.log('可用图标:', allIcons)

// 获取图标详细配置
const configs = getIconConfigs()
console.log('图标配置:', configs)
```

## 高级用法

### 自定义图标生成器

```typescript
import { IconGenerator } from '@/utils/tools/icon-generator'

// 创建自定义图标生成器
const customIconGenerator = new IconGenerator()

// 从不同目录加载图标
await customIconGenerator.loadIconsFromGlob('@/assets/custom-icons/*.svg', {
  nameTransform: (fileName) => `Custom${fileName}`
})

// 手动注册图标
customIconGenerator.registerIcon('MyIcon', () => renderIcon(MyIconComponent))
```

### 批量导入图标

```typescript
import { iconGenerator } from '@/utils/tools/icon-generator'

// 从多个目录加载图标
await iconGenerator.loadIconsFromGlob('@/assets/icons/outline/*.svg', {
  nameTransform: (fileName) => `Outline${fileName}`
})

await iconGenerator.loadIconsFromGlob('@/assets/icons/filled/*.svg', {
  nameTransform: (fileName) => `Filled${fileName}`
})
```

## 注意事项

1. **SVG文件要求**：确保SVG文件格式正确，包含 `viewBox` 属性
2. **命名规范**：使用kebab-case或snake_case命名SVG文件
3. **性能优化**：图标采用异步加载，首次使用时才会加载对应的SVG文件
4. **类型安全**：所有图标都有完整的TypeScript类型支持

## 示例组件

查看 `src/components/common/IconDemo.vue` 了解完整的使用示例。

## 故障排除

### 图标不显示
1. 检查SVG文件是否在正确的目录下
2. 确认文件名是否符合命名规范
3. 检查控制台是否有导入错误

### TypeScript错误
1. 确保已导入正确的类型定义
2. 检查图标名称是否正确
3. 使用 `hasIcon()` 函数进行运行时检查
